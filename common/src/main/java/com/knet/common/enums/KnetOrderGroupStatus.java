package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/12 10:33
 * @description: knet订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetOrderGroupStatus {
    /**
     * 基础状态（用户侧）
     */
    PENDING_PAYMENT(0, "PENDING_PAYMENT", "待支付"),
    PAY_FAILED(1, "PAY_FAILED", "支付失败"),
    PAID(2, "PAID", "已支付"),
    /**
     * 物流状态（用户侧）
     */
    PENDING_SHIPMENT(3, "PENDING_SHIPMENT", "待发货"),
    PARTIALLY_SHIPPED(4, "PARTIALLY_SHIPPED", "部分发货"),
    SHIPPED(5, "SHIPPED", "已发货"),
    DELIVERED(6, "DELIVERED", "已送达"),
    COMPLETED(7, "COMPLETED", "已完成"),
    PARTIALLY_CANCELLED(8, "PARTIALLY_CANCELLED", "部分取消"),
    CANCELLED(9, "CANCELLED", "已取消"),
    /**
     * 售后状态
     */
    REFUNDING(10, "REFUNDING", "退款中"),
    RETURNING(11, "RETURNING", "退货中"),
    /**
     * 特殊状态，订单被KG拉走，不能取消
     */
    FROZEN(12, "FROZEN", "已冻结");

    /**
     * 状态代码
     */
    @EnumValue
    private final Integer code;

    /**
     * 状态名称
     */
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public static KnetOrderGroupStatus fromCode(int code) {
        for (KnetOrderGroupStatus status : KnetOrderGroupStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
