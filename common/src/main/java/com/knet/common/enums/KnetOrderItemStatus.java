package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:09
 * @description: 订单明细表-订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetOrderItemStatus {
    /**
     * 支付状态
     */
    PENDING_PAYMENT(0, "PENDING_PAYMENT", "待支付"),
    PAID(1, "PAID", "已支付"),
    PAY_FAILED(7, "PAY_FAILED", "支付失败"),
    /**
     * 物流状态
     */
    PENDING_SHIPMENT(2, "PENDING_SHIPMENT", "待发货"),
    SHIPPED(3, "SHIPPED", "已发货"),
    DELIVERED(4, "DELIVERED", "已送达"),
    COMPLETED(5, "COMPLETED", "已完成"),
    CANCELLED(6, "CANCELLED", "已取消"),
    /**
     * 特殊状态，订单被KG拉走，不能取消
     */
    FROZEN(8, "FROZEN", "已冻结");
    /**
     * 状态代码
     */
    @EnumValue
    private final Integer code;
    /**
     * 状态名称
     */
    @JsonValue
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    public static KnetOrderItemStatus fromCode(int code) {
        for (KnetOrderItemStatus status : KnetOrderItemStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
