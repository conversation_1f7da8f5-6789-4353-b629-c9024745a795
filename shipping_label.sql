CREATE TABLE `shipping_label` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(255) DEFAULT NULL COMMENT '批次编号',
  `tracking_number` varchar(255) DEFAULT NULL COMMENT '物流单号',
  `label_url` varchar(255) DEFAULT NULL COMMENT '电子面单地址',
  `express_company` varchar(255) DEFAULT NULL COMMENT '物流公司',
  `package_count` int DEFAULT NULL COMMENT '总包裹数',
  `shipping_fee` bigint DEFAULT NULL COMMENT '运费(单位：分)',
  `warehouse` varchar(255) DEFAULT NULL COMMENT '发货仓库',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0 表示未删除， 1 表示已删除',
  `version` int NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
  PRIMARY KEY (`id`),
  INDEX `idx_tracking_number` (`tracking_number`) COMMENT '物流单号索引',
  INDEX `idx_batch_no` (`batch_no`) COMMENT '批次编号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物流运单表';
