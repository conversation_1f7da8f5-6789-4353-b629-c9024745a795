package com.knet.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.resp.CreateOrderResponse;
import com.knet.order.model.dto.resp.OrderDetailResponse;
import com.knet.order.model.dto.resp.OrderListResponse;
import com.knet.order.model.entity.SysOrderGroup;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:16
 * @description: 订单聚合服务接口定义
 */
public interface ISysOrderProcessService {

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    CreateOrderResponse createOrderFromCart(CreateOrderRequest request);

    /**
     * 分页查询订单列表
     *
     * @param request 查询请求
     * @return 订单列表响应
     */
    IPage<OrderListResponse.ParentOrderResponse> queryOrderList(OrderListQueryRequest request);

    /**
     * 获取订单详细信息
     *
     * @param orderId 订单号
     * @return 订单详细信息
     */
    OrderDetailResponse getOrderDetail(String orderId);

    /**
     * 取消订单
     *
     * @param orderId 订单号
     */
    void cancelOrder(String orderId);

    /**
     * 更新订单状态为冻结
     * 同时更新订单组、子订单和订单项的状态为冻结
     *
     * @param orderGroup 订单组信息
     * @return 更新结果，true表示成功，false表示失败
     */
    boolean freezeOrder(SysOrderGroup orderGroup);

    /**
     * 处理库存扣减失败补偿
     *
     * @param messageBody 消息体
     */
    void processInventoryFailed(String messageBody);
}
