package com.knet.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.order.mapper.SysShippingLabelMapper;
import com.knet.order.model.entity.SysShippingLabel;
import com.knet.order.service.ISysShippingLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:55:02
 * @description: 针对表【sys_shipping_label(物流运单)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysShippingLabelServiceImpl extends ServiceImpl<SysShippingLabelMapper, SysShippingLabel> implements ISysShippingLabelService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysShippingLabel createShippingLabel(SysShippingLabel shippingLabel) {
        log.info("创建物流运单: batchNo={}, trackingNumber={}", shippingLabel.getBatchNo(), shippingLabel.getTrackingNumber());
        boolean saved = this.save(shippingLabel);
        if (saved) {
            log.info("物流运单创建成功: id={}, trackingNumber={}", shippingLabel.getId(), shippingLabel.getTrackingNumber());
            return shippingLabel;
        } else {
            log.error("物流运单创建失败: batchNo={}, trackingNumber={}", shippingLabel.getBatchNo(), shippingLabel.getTrackingNumber());
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SysShippingLabel> batchCreateShippingLabels(List<SysShippingLabel> shippingLabels) {
        log.info("批量创建物流运单, 数量: {}", shippingLabels.size());
        boolean saved = this.saveBatch(shippingLabels);
        if (saved) {
            log.info("批量创建物流运单成功, 数量: {}", shippingLabels.size());
            return shippingLabels;
        } else {
            log.error("批量创建物流运单失败, 数量: {}", shippingLabels.size());
            return null;
        }
    }

    @Override
    public List<SysShippingLabel> getShippingLabelsByBatchNo(String batchNo) {
        log.info("根据批次号查询物流运单: batchNo={}", batchNo);
        LambdaQueryWrapper<SysShippingLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysShippingLabel::getBatchNo, batchNo);
        List<SysShippingLabel> labels = this.list(queryWrapper);
        log.info("根据批次号查询物流运单结果: batchNo={}, count={}", batchNo, labels.size());
        return labels;
    }

    @Override
    public SysShippingLabel getShippingLabelByTrackingNumber(String trackingNumber) {
        log.info("根据物流单号查询物流运单: trackingNumber={}", trackingNumber);
        LambdaQueryWrapper<SysShippingLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysShippingLabel::getTrackingNumber, trackingNumber);
        SysShippingLabel label = this.getOne(queryWrapper);
        if (label != null) {
            log.info("根据物流单号查询物流运单成功: trackingNumber={}, id={}", trackingNumber, label.getId());
        } else {
            log.warn("根据物流单号查询物流运单失败，未找到记录: trackingNumber={}", trackingNumber);
        }
        return label;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateShippingLabel(SysShippingLabel shippingLabel) {
        log.info("更新物流运单: id={}, trackingNumber={}", shippingLabel.getId(), shippingLabel.getTrackingNumber());
        try {
            boolean updated = this.updateById(shippingLabel);
            log.info("更新物流运单结果: id={}, trackingNumber={}, result={}",
                    shippingLabel.getId(), shippingLabel.getTrackingNumber(), updated);
            return updated;
        } catch (Exception e) {
            log.error("更新物流运单失败: id={}, trackingNumber={}, error={}",
                    shippingLabel.getId(), shippingLabel.getTrackingNumber(), e.getMessage(), e);
            return false;
        }
    }
}
