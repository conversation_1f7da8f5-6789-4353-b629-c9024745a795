package com.knet.order.system.schedule;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.event.OrderFrozenEvent;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.knet.common.constants.OrderServicesConstants.ORDER_PAID_TIMEOUT_TIME;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:47
 * @description: 订单待发货定时任务
 */
@Slf4j
@Component
public class OrderNeedToShipJob {
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ISysOrderProcessService orderProcessService;

    /**
     * 每5分钟执行一次，订单已经支付转换为FROZEN 冻结
     */
    @XxlJob("needToShipOrder")
    public ReturnT<String> needToShipOrder() {
        log.info("任务 needToShipOrder 开始执行 ");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("needToShipOrder");
        stopWatch.start();
        // 查询条件：已支付状态的订单
        LambdaQueryWrapper<SysOrderGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderGroup::getStatus, KnetOrderGroupStatus.PAID);
        // 计算订单支付超时时间点
        LocalDateTime timeoutThreshold = LocalDateTime.now().minus(Duration.ofMinutes(ORDER_PAID_TIMEOUT_TIME));
        queryWrapper.lt(SysOrderGroup::getUpdateTime, timeoutThreshold);
        // 一次性获取所有已支付状态的订单
        List<SysOrderGroup> orderGroups = orderGroupService.list(queryWrapper);
        long updateCount = 0;
        if (CollUtil.isNotEmpty(orderGroups)) {
            updateCount = orderGroups.stream().filter(this::frozenOrderAndPushEvent).count();
            log.info("Stream处理完成: 成功处理{}条订单", updateCount);
        } else {
            log.info("无符合条件订单");
        }
        // 记录执行结果
        stopWatch.stop();
        String resultMsg = updateCount > 0 ? "成功更新" + updateCount + "条订单状态为冻结" : "无符合条件订单";
        log.info("任务结束: {}", resultMsg);
        XxlJobHelper.log(resultMsg + "，耗时: {}ms", stopWatch.getTotalTimeMillis());
        return ReturnT.SUCCESS;
    }

    /**
     * 冻结订单并推送事件
     *
     * @param orderGroup 订单组
     * @return 是否成功
     */
    private boolean frozenOrderAndPushEvent(SysOrderGroup orderGroup) {
        boolean updateResult = orderProcessService.freezeOrder(orderGroup);
        if (updateResult) {
            applicationEventPublisher.publishEvent(new OrderFrozenEvent(this, orderGroup.getOrderId()));
            log.info("为订单组 {} 发送了 OrderPendingShipmentEvent 事件", orderGroup.getId());
            return true;
        } else {
            log.warn("订单组 {} 状态更新失败", orderGroup.getId());
            return false;
        }
    }
}
