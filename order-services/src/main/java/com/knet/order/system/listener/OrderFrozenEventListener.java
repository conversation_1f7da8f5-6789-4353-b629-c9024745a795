package com.knet.order.system.listener;

import com.knet.order.system.event.OrderFrozenEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2025/6/13 16:00
 * @description: 订单待发货监听器
 */
@Component
public class OrderFrozenEventListener {

    @TransactionalEventListener(
            classes = OrderFrozenEvent.class,
            phase = TransactionPhase.AFTER_COMPLETION
    )
    public void handleOrderFrozenEvent(OrderFrozenEvent event) {
    }
}
