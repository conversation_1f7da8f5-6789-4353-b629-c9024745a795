package com.knet.order.mq.consumer;

import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.IOrderCompensationService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.ORDER_TIME_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/3/19 13:34
 * @description: 订单消费者
 */
@Slf4j
@Component
public class OrderTimeoutConsumer {

    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private IOrderCompensationService orderCompensationService;

    @RabbitListener(
            queues = "timeout.order.queue.order-services",
            ackMode = "MANUAL"
    )
    public void handleInventoryFailed(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("timeout.order".equals(routingKey)) {
                if (!redisCacheUtil.setIfAbsent(ORDER_TIME_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("timeout.order 服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                orderCompensationService.processOrderTimeout(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("订单服务处理订单超时 补偿处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }
}
